import BasePanel from './panels/BasePanel.js';

/**
 * 经济面板 - 显示经济系统信息
 */
class EconomicPanel extends BasePanel {
  constructor(scene, x, y) {
    super(scene, x, y, {
      width: 400,
      height: 500,
      title: '经济系统',
      onClose: () => this.hide()
    });

    this.economicSystem = scene.economicSystem;
    this.createContent();
  }

  createContent() {
    const contentY = 50;

    // 货币显示
    this.currencyText = this.scene.add.text(20, contentY, '', {
      fontSize: '16px',
      fill: '#ffffff'
    }).setOrigin(0.5,0);
    this.add(this.currencyText);

    // 价格信息
    this.priceText = this.scene.add.text(20, contentY + 80, '', {
      fontSize: '14px',
      fill: '#ffffff'
    }).setOrigin(0);
    this.add(this.priceText);

    // 满意度信息
    this.satisfactionText = this.scene.add.text(20, contentY + 200, '', {
      fontSize: '14px',
      fill: '#ffffff'
    }).setOrigin(0);
    this.add(this.satisfactionText);

    // 天气信息
    this.weatherText = this.scene.add.text(20, contentY + 280, '', {
      fontSize: '14px',
      fill: '#ffffff'
    }).setOrigin(0);
    this.add(this.weatherText);

    // 移民吸引力
    this.immigrationText = this.scene.add.text(20, contentY + 320, '', {
      fontSize: '14px',
      fill: '#ffffff'
    }).setOrigin(0);
    this.add(this.immigrationText);

    // 事件信息
    this.eventText = this.scene.add.text(20, contentY + 360, '', {
      fontSize: '14px',
      fill: '#ffff00'
    }).setOrigin(0);
    this.add(this.eventText);

    // 监听随机事件
    this.scene.events.on('randomEvent', (event) => {
      this.showEvent(event);
    });

    this.updateContent();
  }

  updateContent() {
    if (!this.economicSystem) return;

    // 更新货币显示
    const currency = this.economicSystem.currency;
    this.currencyText.setText(
      `货币系统:\n` +
      `金币: ${currency.gold}\n` +
      `银币: ${currency.silver}\n` +
      `铜币: ${currency.copper}`
    );

    // 更新价格信息
    const prices = [];
    Object.keys(this.economicSystem.basePrices).slice(0, 6).forEach(resource => {
      const price = this.economicSystem.getPrice(resource);
      const basePrice = this.economicSystem.basePrices[resource];
      const change = price > basePrice ? '↑' : price < basePrice ? '↓' : '=';
      prices.push(`${resource}: ${price}铜币 ${change}`);
    });

    this.priceText.setText(`当前价格:\n${prices.join('\n')}`);

    // 更新满意度信息
    const satisfaction = this.economicSystem.getSatisfaction();
    const factors = this.economicSystem.satisfaction.factors;
    this.satisfactionText.setText(
      `人口满意度: ${satisfaction.toFixed(1)}%\n` +
      `食物: ${factors.food.toFixed(1)}\n` +
      `就业: ${factors.employment.toFixed(1)}\n` +
      `税收: ${factors.taxes.toFixed(1)}`
    );

    // 更新天气信息
    const weather = this.economicSystem.weather;
    const weatherEffect = this.economicSystem.getWeatherEffect();
    this.weatherText.setText(
      `天气: ${this.getWeatherName(weather.current)}\n` +
      `生产效率: ${(weatherEffect.productionModifier * 100).toFixed(0)}%`
    );

    // 更新移民吸引力
    const attraction = this.economicSystem.getImmigrationAttraction();
    this.immigrationText.setText(
      `移民吸引力: ${attraction > 0 ? '+' : ''}${attraction}%`
    );
  }

  getWeatherName(weather) {
    switch(weather) {
      case 'normal': return '正常';
      case 'rain': return '雨季';
      case 'drought': return '干旱';
      default: return '未知';
    }
  }

  showEvent(event) {
    this.eventText.setText(`事件: ${event.name}\n${event.description}`);

    // 5秒后清除事件显示
    this.scene.time.delayedCall(5000, () => {
      this.eventText.setText('');
    });
  }

  show() {
    super.show();
    this.updateContent();
  }

  update() {
    if (this.container.visible) {
      this.updateContent();
    }
  }
}

export default EconomicPanel;
