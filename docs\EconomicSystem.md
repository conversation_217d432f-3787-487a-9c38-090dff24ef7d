# 中世纪经济系统设计文档

## 概述
本文档描述了游戏中实现的中世纪幻想城市建设经济系统，基于真实的中世纪经济模型，包含农业、手工业、贸易、人口管理等核心要素。

## 核心系统

### 1. 货币系统
- **铜币**: 基础货币单位，1铜币 = 1份小麦的基准价值
- **银币**: 1银币 = 100铜币
- **金币**: 1金币 = 100银币 = 10000铜币

### 2. 基础资源与职业体系

#### 农业系统
- **农场**: 20农民/天产出100份小麦
- **肥力机制**: 每季度消耗1单位粪肥，否则减产20%
- **天灾影响**: 雨季减产30%，旱灾减产50%
- **灌溉系统**: 可建造灌溉渠抵御天灾
- **农民薪酬**: 日薪3份小麦，生存需求1份/天

#### 资源采集职业
- **矿工**: 10人/日产出20单位铁矿，日薪4份小麦，需铁镐
- **伐木工**: 5人/日产出50单位木材，日薪3.5份小麦，需斧头
- **猎人**: 3人/日产出5份肉类，日薪4份小麦，需猎犬
- **渔民**: 8人/日产出15份鱼类，日薪3.5份小麦，需渔船

### 3. 产业链加工

#### 农业→食品加工链
- **磨坊**: 2工人，10小麦 → 1份面粉
- **面包房**: 1面粉 + 0.5酵母 → 2个面包
- **市场价值**: 1面包 = 5铜币，可满足2天生存需求

#### 资源→工具加工链
- **铁匠铺**: 5铁矿 + 2木材 → 1把铁镐（售价20铜币）
- **木匠铺**: 10木材 → 1张犁（售价50铜币，提高农田产量200份/年）

### 4. 市场机制

#### 供需价格系统
- **价格公式**: 价格 = 基准价 × (1 + 供需差率)
- **价格范围**: 0.5倍 - 2.0倍基准价
- **库存影响**: 
  - 小麦库存 > 500时降至0.8铜币
  - 小麦库存 < 100时涨至1.5铜币

#### 调控手段
- **谷仓**: 容量500，可囤货平抑价格
- **市场建筑**: 提升交易效率50%，减少价格波动

### 5. 税收系统
- **土地税**: 对农田/矿场征收10%产出
- **商业税**: 对市场交易征收5%交易额
- **人头税**: 2铜币/人

### 6. 人口与满意度系统

#### 人口增长公式
```
日增长数 = (食物盈余/10) - (税收负担×0.1) - (疾病率×5)
```

#### 满意度影响因素
- **食物供应**: 基于食物供需比例
- **就业情况**: 失业率影响满意度
- **税收负担**: 税收越高满意度越低
- **健康水平**: 基础设施影响健康

#### 满意度效果
- **>80%**: 工作效率+30%，移民吸引力+20%
- **60-80%**: 工作效率+10%，移民吸引力+15%
- **40-60%**: 正常水平
- **<30%**: 工作效率-20%，移民吸引力-10%

### 7. 移民吸引力系统
- **就业机会**: 工作职位多于人口时+30%
- **失业率**: >30%时-60%
- **人口满意度**: 高满意度增加吸引力

### 8. 公共建设系统
- **水井**: 消耗50石头，降低疾病率30%
- **市场**: 消耗200木材，提升交易效率50%
- **城堡**: 消耗1000石头+500木材，解锁征兵，增加税收负担20%
- **医院**: 消耗200银币，治疗瘟疫，提高健康水平

### 9. 突发事件系统

#### 天气事件
- **雨季**: 农作物减产30%，持续1分钟
- **旱灾**: 农作物减产50%，持续1.5分钟

#### 经济事件
- **商路阻断**: 奢侈品价格上涨300%
- **瘟疫**: 人口死亡率15%，生产效率下降40%
- **战争**: 敌军掠夺30%农田资源，市场价格翻倍

## 实现的文件结构

### 核心系统文件
- `src/systems/EconomicSystem.js` - 经济系统主控制器
- `src/ui/EconomicPanel.js` - 经济信息显示面板

### 配置文件
- `assets/data/resources.json` - 资源定义和基准价格
- `assets/data/buildings.json` - 建筑配置和经济参数

### 修改的现有文件
- `src/entities/Building.js` - 添加经济系统参数支持
- `src/systems/PopulationSystem.js` - 添加新职业类型
- `src/scenes/GameScene.js` - 集成经济系统
- `src/systems/UIManager.js` - 添加经济面板管理

## 游戏平衡设计要点

### 1. 生存资源平衡
农民需保留足够口粮，避免过度投放市场导致饥荒。

### 2. 加工产业投资回报
面包等高利润产品需要前置建设成本（磨坊、面包房），需权衡投入产出。

### 3. 税收动态调节
高税收加速资金积累，但可能导致人口流失，需在扩张期与稳定期调整。

### 4. 技术升级链条
木材→工具→采矿→高级工具的正循环，驱动生产效率提升。

## 使用方法

### 启动经济系统
经济系统在游戏启动时自动初始化，通过点击"经济"按钮可以查看经济面板。

### 查看经济信息
经济面板显示：
- 当前货币状况
- 资源价格变化
- 人口满意度
- 天气状况
- 移民吸引力
- 随机事件信息

### 建筑经济参数
新建筑支持以下经济参数：
- `dailyWage`: 工人日薪
- `toolConsumption`: 工具消耗
- `animalRequirement`: 动物需求
- `equipmentMaintenance`: 设备维护
- `fertilityRequired`: 肥力需求
- `healthBonus`: 健康加成
- `tradeEfficiencyBonus`: 贸易效率加成

## 未来扩展

### 计划功能
1. 贸易路线系统
2. 外交和战争系统
3. 科技研发树
4. 季节性事件
5. 更复杂的社会阶层系统

### 平衡调整
根据游戏测试反馈，可能需要调整：
- 资源产出比例
- 价格波动范围
- 事件发生频率
- 人口增长速度
